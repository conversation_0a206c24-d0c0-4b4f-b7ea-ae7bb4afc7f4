/* 关键路径 CSS - 首屏渲染必需 */
html, body {
  margin: 0;
  padding: 0;
  font-family: var(--font-inter), system-ui, sans-serif;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 防止布局偏移的基础样式 */
img, video {
  max-width: 100%;
  height: auto;
}

/* 加载状态样式 */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* 关键按钮样式 */
.btn-primary {
  background: #0070f3;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.btn-primary:hover {
  background: #0051cc;
}

/* 数据节省模式样式 */
.data-saver-mode img[loading="lazy"] {
  background: #f0f0f0;
}

.data-saver-mode video {
  display: none;
}

.data-saver-mode .animation {
  animation: none !important;
}

.data-saver-mode .parallax {
  transform: none !important;
}
